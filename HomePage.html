<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }
        .lucide {
            width: 1.25rem; /* Standard icon size */
            height: 1.25rem;
            stroke-width: 2px;
            flex-shrink: 0; /* Prevent icons from shrinking */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; }
        ::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 10px; }
        ::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
        
        /* Styles for the modal */
        #ai-modal-backdrop { transition: opacity 0.3s ease-in-out; }
        #ai-modal-panel { transition: all 0.3s ease-in-out; }
        
        /* Animation for loading spinner */
        .spinner { animation: spin 1s linear infinite; }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Ensure smooth transitions on sidebar and main content */
        #sidebar, #main-content {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div class="flex h-screen bg-white">
        <!-- Sidebar Navigation -->
        <aside id="sidebar" class="w-20 bg-gray-800 text-gray-200 flex flex-col fixed inset-y-0 left-0 z-30 overflow-hidden">
            <!-- Logo -->
            <div class="h-20 flex items-center justify-center px-4 border-b border-gray-700 shrink-0">
                <!-- Logo container that adapts to sidebar width -->
                <div id="logo-container" class="bg-white p-2 rounded-lg shadow-sm w-12 h-12 flex items-center justify-center transition-all duration-300">
                    <img src="https://www.lohiagroup.com/images/logo.png" alt="LohiaCorp Logo" class="h-8 w-auto transition-opacity duration-300">
                </div>
            </div>

            <!-- Nav Links -->
            <nav class="flex-1 overflow-y-auto overflow-x-hidden">
                <ul class="p-4 space-y-2">
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="layout-dashboard"></i><span class="nav-text whitespace-nowrap opacity-0">Dashboard</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-sm"><i data-lucide="home"></i><span class="nav-text whitespace-nowrap opacity-0">Home</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="search"></i><span class="nav-text whitespace-nowrap opacity-0">Part Finder</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="shopping-cart"></i><span class="nav-text whitespace-nowrap opacity-0">Cart</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="package-plus"></i><span class="nav-text whitespace-nowrap opacity-0">Part Ordering</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="file-check-2"></i><span class="nav-text whitespace-nowrap opacity-0">Created Orders</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="truck"></i><span class="nav-text whitespace-nowrap opacity-0">Track Orders</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="archive"></i><span class="nav-text whitespace-nowrap opacity-0">Closed Orders</span></a></li>
                    <li><a href="#" class="nav-link flex items-center gap-3 px-3 py-2.5 hover:bg-gray-700 rounded-lg transition-colors"><i data-lucide="info"></i><span class="nav-text whitespace-nowrap opacity-0">About Us</span></a></li>
                </ul>
            </nav>
            
            <!-- Sidebar Footer / User Info -->
            <div class="p-4 border-t border-gray-700 shrink-0"><a href="#" class="flex items-center gap-3 group"><img src="https://placehold.co/40x40/E2E8F0/4A5568?text=K" alt="User Avatar" class="w-10 h-10 rounded-full flex-shrink-0"><div class="flex-1 nav-text opacity-0 transition-opacity duration-200"><p class="font-semibold text-white text-sm whitespace-nowrap">Kanpur Plastipack</p><p class="text-xs text-gray-400 whitespace-nowrap">ID: 10340</p></div><i data-lucide="log-out" class="nav-text opacity-0 transition-opacity duration-200"></i></a></div>
        </aside>

        <!-- Main Content -->
        <main id="main-content" class="flex-1 flex flex-col h-screen sm:ml-20">
            <!-- Header -->
            <header class="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 sm:px-6 shrink-0">
                <h1 class="text-xl font-bold text-gray-900">Order Management System</h1>
                <div class="flex items-center gap-4">
                    <button class="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800"><i data-lucide="shield-check"></i><span>Parts Policy</span></button>
                    <div class="w-px h-6 bg-gray-300 hidden sm:block"></div>
                    <div class="text-right hidden sm:block">
                        <div class="text-sm font-medium text-gray-700">Welcome, Kanpur Plastipack Limited</div>
                        <div class="text-xs text-gray-500"><EMAIL></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    <!-- Search Forms Section -->
                    <div class="bg-white p-6 rounded-2xl shadow-lg">
                        <div class="border-b border-gray-200 mb-6"><nav class="flex space-x-6" id="search-tabs"><button data-tab="numberEnquiry" class="tab-button text-sm sm:text-base font-semibold text-yellow-600 border-b-2 border-yellow-500 py-3 px-1">Parts Number Enquiry</button><button data-tab="priceEnquiry" class="tab-button text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 py-3 px-1">Price Enquiry</button><button data-tab="aiSearch" class="tab-button text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 py-3 px-1 flex items-center gap-2">✨ AI-Powered Search</button></nav></div>
                        <div id="form-container">
                            <div id="numberEnquiry" class="tab-content space-y-8"><div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-start"><form class="p-6 border border-gray-200 rounded-xl space-y-4"><h3 class="font-semibold text-gray-800">Part Search by Catalog</h3><div><label for="catalogNumber" class="text-sm font-medium text-gray-600">Catalog Number</label><div class="relative mt-1"><input type="text" id="catalogNumber" class="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500"><button class="absolute inset-y-0 right-0 flex items-center px-3 bg-gray-100 hover:bg-gray-200 rounded-r-lg"><i data-lucide="search"></i></button></div></div></form><form class="p-6 border border-gray-200 rounded-xl space-y-4"><h3 class="font-semibold text-gray-800">Part Search by M/C Serial No and Model</h3><div><label for="mfgYear" class="text-sm font-medium text-gray-600">MFG Year</label><select id="mfgYear" class="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500"><option>--Select--</option><option>2025</option><option>2024</option><option>2023</option></select></div><div><label for="modelDesc" class="text-sm font-medium text-gray-600">Model Description</label><select id="modelDesc" class="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500"><option>--Select--</option><option>Model A</option><option>Model B</option></select></div><div><label for="serialNumber" class="text-sm font-medium text-gray-600">Serial Number</label><div class="relative mt-1"><select id="serialNumber" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500 appearance-none pr-10"><option>--Select--</option></select><button class="absolute inset-y-0 right-0 flex items-center px-3 bg-gray-100 hover:bg-gray-200 rounded-r-lg"><i data-lucide="search"></i></button></div></div></form></div></div>
                            <div id="priceEnquiry" class="tab-content hidden"><form id="priceEnquiryForm" class="p-6 border border-gray-200 rounded-xl space-y-4 max-w-md"><h3 class="font-semibold text-gray-800">Price Enquiry</h3><div><label for="partNumber" class="text-sm font-medium text-gray-600">Part Number</label><div class="relative mt-1"><input type="text" id="partNumber" placeholder="Enter Part Number" class="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500"><button type="button" class="absolute inset-y-0 right-0 flex items-center px-3 bg-gray-100 hover:bg-gray-200 rounded-r-lg"><i data-lucide="search"></i></button></div></div><div class="flex items-center gap-2"><button type="submit" class="w-full bg-yellow-500 text-gray-900 font-bold py-2.5 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-4 focus:ring-yellow-500/50 transition-all">Check Price</button><button id="getAiAnalysis" type="button" class="w-full bg-blue-600 text-white font-bold py-2.5 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 transition-all flex items-center justify-center gap-2"> ✨ Get AI Analysis</button></div></form></div>
                            <div id="aiSearch" class="tab-content hidden"><form id="aiSearchForm" class="p-6 border border-gray-200 rounded-xl space-y-4"><h3 class="font-semibold text-gray-800">✨ Search for a Part by Description</h3><div><label for="partDescription" class="text-sm font-medium text-gray-600">Describe the part you need</label><textarea id="partDescription" rows="4" placeholder="e.g., 'A reinforced drive belt for the main assembly of a 2023 Model X weaver...'" class="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-yellow-500 focus:border-yellow-500"></textarea></div><button type="submit" class="w-full bg-blue-600 text-white font-bold py-2.5 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 transition-all">Find Part Number</button></form></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- AI Results Modal -->
    <div id="ai-modal" class="fixed inset-0 z-50 hidden"><div id="ai-modal-backdrop" class="fixed inset-0 bg-black/50"></div><div class="relative flex items-center justify-center min-h-screen p-4"><div id="ai-modal-panel" class="relative w-full max-w-2xl bg-white rounded-2xl shadow-xl transform opacity-0 scale-95"><div class="flex items-center justify-between p-4 border-b border-gray-200"><h3 id="ai-modal-title" class="text-lg font-semibold">AI Assistant</h3><button id="close-ai-modal" class="text-gray-500 hover:text-gray-800"><i data-lucide="x"></i></button></div><div class="p-6"><div id="ai-modal-loading" class="text-center py-12 hidden"><div class="spinner w-12 h-12 border-4 border-yellow-500 border-t-transparent rounded-full mx-auto mb-4"></div><p class="font-medium">AI is thinking...</p><p class="text-sm text-gray-500">Please wait a moment.</p></div><div id="ai-modal-content" class="prose max-w-none"></div></div></div></div></div>

    <script>
        lucide.createIcons();

        // --- Gemini-style Hover-based Sidebar Logic ---
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const navTexts = document.querySelectorAll('.nav-text');

        const expandSidebar = () => {
            sidebar.classList.remove('w-20');
            sidebar.classList.add('w-64');
            mainContent.classList.remove('sm:ml-20');
            mainContent.classList.add('sm:ml-64');
            navTexts.forEach(text => {
                text.classList.remove('opacity-0');
            });
        };

        const collapseSidebar = () => {
            sidebar.classList.add('w-20');
            sidebar.classList.remove('w-64');
            mainContent.classList.add('sm:ml-20');
            mainContent.classList.remove('sm:ml-64');
             navTexts.forEach(text => {
                text.classList.add('opacity-0');
            });
        };

        sidebar.addEventListener('mouseenter', expandSidebar);
        sidebar.addEventListener('mouseleave', collapseSidebar);


        // --- Tab Switching Logic ---
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                tabButtons.forEach(btn => {
                    btn.classList.remove('text-yellow-600', 'border-yellow-500');
                    btn.classList.add('text-gray-500', 'hover:text-gray-700');
                });
                button.classList.add('text-yellow-600', 'border-yellow-500');
                button.classList.remove('text-gray-500', 'hover:text-gray-700');
                tabContents.forEach(content => {
                    content.classList.toggle('hidden', content.id !== tabId);
                });
            });
        });
        
        // --- AI Modal Logic ---
        const modal = document.getElementById('ai-modal');
        const modalBackdrop = document.getElementById('ai-modal-backdrop');
        const modalPanel = document.getElementById('ai-modal-panel');
        const closeModalButton = document.getElementById('close-ai-modal');
        const modalTitle = document.getElementById('ai-modal-title');
        const modalLoading = document.getElementById('ai-modal-loading');
        const modalContent = document.getElementById('ai-modal-content');

        function showModal() {
            modal.classList.remove('hidden');
            setTimeout(() => {
                modalBackdrop.classList.remove('opacity-0');
                modalPanel.classList.remove('opacity-0', 'scale-95');
            }, 10);
        }

        function hideModal() {
            modalBackdrop.classList.add('opacity-0');
            modalPanel.classList.add('opacity-0', 'scale-95');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        function setModalLoading(isLoading) {
            modalLoading.classList.toggle('hidden', !isLoading);
            modalContent.classList.toggle('hidden', isLoading);
        }

        closeModalButton.addEventListener('click', hideModal);
        modalBackdrop.addEventListener('click', hideModal);

        // --- Gemini API Call ---
        async function callGeminiAPI(prompt, isJson = false) {
            const apiKey = ""; // Leave blank
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            let payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
            if (isJson) {
                payload.generationConfig = {
                    responseMimeType: "application/json",
                    responseSchema: { type: "ARRAY", items: { type: "OBJECT", properties: { partNumber: { "type": "STRING" }, reason: { "type": "STRING" } }, required: ["partNumber", "reason"] } }
                };
            }
            try {
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
                if (!response.ok) throw new Error(`API call failed with status: ${response.status}`);
                const result = await response.json();
                if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                    return result.candidates[0].content.parts[0].text;
                } else {
                    console.error("Unexpected API response structure:", result);
                    return "Error: Could not get a valid response from the AI.";
                }
            } catch (error) {
                console.error("Error calling Gemini API:", error);
                return `Error: Could not connect to the AI service. ${error.message}`;
            }
        }

        // --- Feature 1: Get AI Analysis for a Part Number ---
        const getAiAnalysisBtn = document.getElementById('getAiAnalysis');
        getAiAnalysisBtn.addEventListener('click', async () => {
            const partNumber = document.getElementById('partNumber').value;
            if (!partNumber) { alert('Please enter a Part Number first.'); return; }
            modalTitle.innerText = `AI Analysis for Part #${partNumber}`;
            showModal();
            setModalLoading(true);
            const prompt = `Provide a detailed analysis for an industrial machine part with the number '${partNumber}'. Include its likely function, common applications, typical materials, and potential maintenance or installation tips. Format the response in clear, well-structured markdown.`;
            const result = await callGeminiAPI(prompt);
            let htmlResult = result.replace(/^### (.*$)/gim, '<h3 class="font-bold text-lg mb-2">$1</h3>').replace(/^## (.*$)/gim, '<h2 class="font-bold text-xl mb-3">$1</h2>').replace(/^# (.*$)/gim, '<h1 class="font-bold text-2xl mb-4">$1</h1>').replace(/\*\*(.*)\*\*/g, '<strong>$1</strong>').replace(/\*(.*)\*/g, '<em>$1</em>').replace(/\n/g, '<br>');
            modalContent.innerHTML = htmlResult;
            setModalLoading(false);
        });

        // --- Feature 2: AI-Powered Part Search by Description ---
        const aiSearchForm = document.getElementById('aiSearchForm');
        aiSearchForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const description = document.getElementById('partDescription').value;
            if (!description) { alert('Please describe the part you are looking for.'); return; }
            modalTitle.innerText = 'AI Part Number Suggestions';
            showModal();
            setModalLoading(true);
            const prompt = `Based on the description "${description}", suggest possible industrial machine part numbers. Provide a list of potential matches with a brief reason for each suggestion.`;
            const jsonString = await callGeminiAPI(prompt, true);
            try {
                const parts = JSON.parse(jsonString);
                let contentHtml = '<p class="mb-4">Here are some possible matches based on your description:</p><ul class="space-y-4">';
                parts.forEach(part => { contentHtml += `<li class="p-3 bg-gray-50 rounded-lg border"><p class="font-semibold text-gray-800">Part Number: <code class="bg-gray-200 text-sm px-2 py-1 rounded">${part.partNumber}</code></p><p class="text-sm text-gray-600 mt-1"><strong>Reason:</strong> ${part.reason}</p></li>`; });
                contentHtml += '</ul>';
                modalContent.innerHTML = contentHtml;
            } catch (error) {
                modalContent.innerHTML = `<p class="text-red-600">Sorry, the AI could not find structured suggestions for your query. Please try rephrasing.</p><p class="text-xs text-gray-500 mt-4">Raw Response: ${jsonString}</p>`;
            }
            setModalLoading(false);
        });
    </script>
</body>
</html>
